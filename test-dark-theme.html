<!DOCTYPE html>
<html lang="zh-CN" data-theme="professional">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色主题测试</title>
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="themes/theme-switcher.css">
    <link rel="stylesheet" href="themes/dark-theme-gradient.css">
    <script src="themes/theme-config.js"></script>
    <script src="themes/theme-manager.js"></script>
</head>
<body>
    <!-- 登录页面测试 -->
    <div id="login-page" style="display: block;">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <span style="font-size: 36px; margin-right: 10px;">📧</span>
                        <h1>跨境运营助手</h1>
                    </div>
                    <p class="login-subtitle">登录您的账号以继续</p>
                </div>
                <div class="login-methods">
                    <button class="google-login-btn btn-primary">
                        <i class="ri-google-fill"></i>
                        使用Google账号登录
                    </button>
                    <div class="login-divider">
                        <span>或</span>
                    </div>
                    <div class="phone-login-form">
                        <div class="login-input-group">
                            <label>手机号码</label>
                            <input type="tel" placeholder="输入您的手机号码" value="+86 13812345678">
                        </div>
                        <div class="login-input-group verification-code-group">
                            <label>验证码</label>
                            <div class="verification-code-container">
                                <input type="text" placeholder="输入验证码" maxlength="6">
                                <button class="send-code-btn btn">发送验证码</button>
                            </div>
                        </div>
                        <button class="phone-login-btn btn-primary">登录</button>
                    </div>
                </div>
                <div class="login-footer">
                    <p>还没有账号？ <a href="#">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用界面测试 -->
    <div id="app" style="display: none;">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-logo">
                <span style="font-size: 28px; margin-right: 8px;">📧</span>
                <h1>跨境运营助手</h1>
            </div>

            <div class="menu-section">
                <h2>主菜单</h2>
                <div class="menu-item active">
                    <div class="menu-item-content">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表盘</span>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-item-content">
                        <i class="fas fa-cube"></i>
                        <span>产品库</span>
                        <span class="counter">1</span>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="menu-item-content">
                        <i class="fas fa-mail-bulk"></i>
                        <span>邮件营销</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <div class="page-title">
                    <h1>仪表盘</h1>
                </div>
                <div class="header-actions">
                    <div class="search-input">
                        <input type="text" placeholder="搜索...">
                    </div>
                    <div class="user-avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&q=80" alt="用户头像">
                    </div>
                </div>
            </div>

            <div class="content-area">
                <div class="stat-cards">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>24</h3>
                            <p>活跃用户</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-info">
                            <h3>13456</h3>
                            <p>发送邮件</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>12.24%</h3>
                            <p>转化率</p>
                        </div>
                    </div>
                </div>

                <div class="content-panel">
                    <h2>最近活动</h2>
                    <div class="list-item">
                        <div class="item-content">
                            <h4>新用户注册</h4>
                            <p>用户 <EMAIL> 注册了账号</p>
                        </div>
                        <div class="item-time">2分钟前</div>
                    </div>
                    <div class="list-item">
                        <div class="item-content">
                            <h4>邮件发送成功</h4>
                            <p>批量邮件已成功发送给 500 位用户</p>
                        </div>
                        <div class="item-time">5分钟前</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题切换器测试按钮 -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
        <button onclick="toggleView()" style="padding: 10px 20px; background: rgba(99, 102, 241, 0.8); color: white; border: none; border-radius: 6px; margin-right: 10px;">
            切换视图
        </button>
        <button onclick="toggleTheme()" style="padding: 10px 20px; background: rgba(139, 92, 246, 0.8); color: white; border: none; border-radius: 6px;">
            切换主题
        </button>
    </div>

    <script>
        function toggleView() {
            const loginPage = document.getElementById('login-page');
            const appPage = document.getElementById('app');
            if (loginPage.style.display === 'none') {
                loginPage.style.display = 'block';
                appPage.style.display = 'none';
            } else {
                loginPage.style.display = 'none';
                appPage.style.display = 'block';
            }
        }

        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            if (currentTheme === 'professional') {
                html.setAttribute('data-theme', 'modern');
            } else {
                html.setAttribute('data-theme', 'professional');
            }
        }

        // 初始化主题管理器
        document.addEventListener('DOMContentLoaded', function() {
            if (window.themeManager) {
                window.themeManager.applyTheme('professional');
            }
        });
    </script>

    <style>
        /* 测试页面特殊样式 */
        .stat-cards {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            flex: 1;
            min-width: 200px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .stat-info h3 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            color: var(--text-primary);
        }

        .stat-info p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .content-area {
            padding: 30px;
        }

        .list-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-content h4 {
            margin: 0 0 5px 0;
            color: var(--text-primary);
        }

        .item-content p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .item-time {
            color: var(--text-tertiary);
            font-size: 12px;
        }

        .header {
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .search-input input {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .user-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .content-panel {
            padding: 25px;
        }

        .content-panel h2 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
        }

        /* 登录页面基础样式 */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            width: 400px;
            max-width: 100%;
            padding: 40px;
            border-radius: 12px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }

        .login-logo h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .login-subtitle {
            color: var(--text-secondary);
            margin: 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
            margin-bottom: 15px;
        }

        .login-divider {
            text-align: center;
            margin: 20px 0;
            color: var(--text-secondary);
            position: relative;
        }

        .login-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
        }

        .login-divider span {
            background: var(--background-color);
            padding: 0 15px;
            position: relative;
        }

        .login-input-group {
            margin-bottom: 20px;
        }

        .login-input-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .login-input-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .verification-code-container {
            display: flex;
            gap: 10px;
        }

        .verification-code-container input {
            flex: 1;
        }

        .send-code-btn {
            padding: 12px 16px;
            background: var(--surface-hover);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
        }
    </style>
</body>
</html>