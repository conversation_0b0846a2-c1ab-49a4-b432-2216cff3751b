/* 深色主题经典黑暗模式 */

/* 登录页面背景 */
[data-theme="professional"] #login-page,
[data-theme="professional"] #google-auth-page,
[data-theme="professional"] #auth-request-page {
    background: #000000;
}

/* 主应用页面背景 */
[data-theme="professional"] body,
[data-theme="professional"] #app {
    background: #000000;
}

/* 登录卡片背景和边框效果 */
[data-theme="professional"] .login-card,
[data-theme="professional"] .google-auth-card,
[data-theme="professional"] .auth-request-card {
    background: #1a1a1a;
    border: 1px solid #333333;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* 侧边栏背景调整 */
[data-theme="professional"] .sidebar {
    background: #1a1a1a;
    border-right: 1px solid #333333;
}

/* 内容区域背景调整 */
[data-theme="professional"] .main-content {
    background: transparent;
}

/* 卡片和面板背景调整 */
[data-theme="professional"] .card,
[data-theme="professional"] .panel,
[data-theme="professional"] .modal-content,
[data-theme="professional"] .dropdown-menu {
    background: #1a1a1a;
    border: 1px solid #333333;
}

/* 表格背景调整 */
[data-theme="professional"] .table-container {
    background: #1a1a1a;
}

[data-theme="professional"] table tbody tr {
    background: #2a2a2a;
}

[data-theme="professional"] table tbody tr:hover {
    background: #3a3a3a;
}

/* 按钮样式调整 */
[data-theme="professional"] .btn-primary {
    background: #007bff;
    border: none;
}

[data-theme="professional"] .btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 输入框样式调整 */
[data-theme="professional"] input,
[data-theme="professional"] textarea,
[data-theme="professional"] select {
    background: #2a2a2a;
    border: 1px solid #333333;
}

[data-theme="professional"] input:focus,
[data-theme="professional"] textarea:focus,
[data-theme="professional"] select:focus {
    background: #3a3a3a;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 菜单项悬停效果 */
[data-theme="professional"] .menu-item:hover {
    background: #2a2a2a;
}

[data-theme="professional"] .menu-item.active {
    background: #3a3a3a;
}

/* 徽章和标签调整 */
[data-theme="professional"] .badge,
[data-theme="professional"] .tag {
    background: #2a2a2a;
    border: 1px solid #333333;
}

/* 成功状态颜色调整 */
[data-theme="professional"] .badge-success,
[data-theme="professional"] .tag-success {
    background: #28a745;
    border-color: #28a745;
}

/* 警告状态颜色调整 */
[data-theme="professional"] .badge-warning,
[data-theme="professional"] .tag-warning {
    background: #ffc107;
    border-color: #ffc107;
}

/* 错误状态颜色调整 */
[data-theme="professional"] .badge-error,
[data-theme="professional"] .tag-error {
    background: #dc3545;
    border-color: #dc3545;
}

/* 移除渐变动画效果，使用简洁的样式 */

/* 滚动条样式 */
[data-theme="professional"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="professional"] ::-webkit-scrollbar-track {
    background: #1a1a1a;
}

[data-theme="professional"] ::-webkit-scrollbar-thumb {
    background: #555555;
    border-radius: 4px;
}

[data-theme="professional"] ::-webkit-scrollbar-thumb:hover {
    background: #666666;
}

/* 统计卡片样式 */
[data-theme="professional"] .stat-card {
    background: #1a1a1a;
    border: 1px solid #333333;
    transition: all 0.3s ease;
}

[data-theme="professional"] .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: #555555;
}

/* 图表容器背景 */
[data-theme="professional"] .chart-container {
    background: #2a2a2a;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 16px;
}

/* 模态框背景 */
[data-theme="professional"] .modal-backdrop {
    background: rgba(0, 0, 0, 0.8);
}

/* 选项卡样式 */
[data-theme="professional"] .tab-item {
    background: transparent;
    color: #888888;
}

[data-theme="professional"] .tab-item:hover {
    background: #2a2a2a;
    color: #cccccc;
}

[data-theme="professional"] .tab-item.active {
    background: #3a3a3a;
    color: #ffffff;
    border-bottom: 2px solid #007bff;
}

/* 侧边栏头部标题样式 */
[data-theme="professional"] .sidebar-logo h1 {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 菜单部分标题 */
[data-theme="professional"] .menu-section h2 {
    color: #888888;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    font-weight: 600;
}

/* 菜单项图标颜色 */
[data-theme="professional"] .menu-item i {
    color: #888888;
    transition: color 0.2s ease;
}

[data-theme="professional"] .menu-item:hover i,
[data-theme="professional"] .menu-item.active i {
    color: #66b3ff;
}

/* 徽章样式重新定义 */
[data-theme="professional"] .counter {
    background: #007bff;
    color: #ffffff;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 头部区域样式 */
[data-theme="professional"] .header {
    background: #1a1a1a;
    border-bottom: 1px solid #333333;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索框样式 */
[data-theme="professional"] .search-input {
    background: #2a2a2a;
    border: 1px solid #333333;
    color: #ffffff;
    placeholder-color: #888888;
}

[data-theme="professional"] .search-input:focus {
    background: #3a3a3a;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

/* 用户头像和下拉菜单 */
[data-theme="professional"] .user-avatar {
    border: 2px solid #333333;
}

[data-theme="professional"] .user-dropdown {
    background: #1a1a1a;
    border: 1px solid #333333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 页面标题样式 */
[data-theme="professional"] .page-title {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 内容面板样式 */
[data-theme="professional"] .content-panel {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 12px;
}

/* 列表项样式 */
[data-theme="professional"] .list-item {
    background: #2a2a2a;
    border: 1px solid #333333;
    transition: all 0.2s ease;
}

[data-theme="professional"] .list-item:hover {
    background: #3a3a3a;
    border-color: #555555;
    transform: translateY(-1px);
}

/* 操作按钮组 */
[data-theme="professional"] .btn-group .btn {
    background: #2a2a2a;
    border: 1px solid #333333;
    color: #cccccc;
}

[data-theme="professional"] .btn-group .btn:hover {
    background: #3a3a3a;
    border-color: #555555;
    color: #ffffff;
}

/* 分页器样式 */
[data-theme="professional"] .pagination .page-link {
    background: #2a2a2a;
    border: 1px solid #333333;
    color: #cccccc;
}

[data-theme="professional"] .pagination .page-link:hover {
    background: #3a3a3a;
    color: #ffffff;
}

[data-theme="professional"] .pagination .page-link.active {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
}

/* 状态指示器 */
[data-theme="professional"] .status-indicator {
    position: relative;
}

[data-theme="professional"] .status-indicator::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    top: -2px;
    right: -2px;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}