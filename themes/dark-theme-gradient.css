/* 深色主题渐变背景效果 */

/* 登录页面渐变背景 */
[data-theme="professional"] #login-page,
[data-theme="professional"] #google-auth-page,
[data-theme="professional"] #auth-request-page {
    background: linear-gradient(135deg, #1a1840 0%, #2a2850 25%, #3a3860 50%, #2a2850 75%, #1a1840 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* 主应用页面渐变背景 */
[data-theme="professional"] body,
[data-theme="professional"] #app {
    background: linear-gradient(135deg, #1a1840 0%, #2a2850 50%, #1a1840 100%);
    background-attachment: fixed;
}

/* 为登录卡片添加更好的背景和边框效果 */
[data-theme="professional"] .login-card,
[data-theme="professional"] .google-auth-card,
[data-theme="professional"] .auth-request-card {
    background: rgba(42, 40, 80, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(58, 56, 96, 0.5);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 侧边栏背景调整 */
[data-theme="professional"] .sidebar {
    background: rgba(42, 40, 80, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(58, 56, 96, 0.5);
}

/* 内容区域背景调整 */
[data-theme="professional"] .main-content {
    background: transparent;
}

/* 卡片和面板背景调整 */
[data-theme="professional"] .card,
[data-theme="professional"] .panel,
[data-theme="professional"] .modal-content,
[data-theme="professional"] .dropdown-menu {
    background: rgba(42, 40, 80, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(58, 56, 96, 0.3);
}

/* 表格背景调整 */
[data-theme="professional"] .table-container {
    background: rgba(42, 40, 80, 0.9);
    backdrop-filter: blur(10px);
}

[data-theme="professional"] table tbody tr {
    background: rgba(42, 40, 80, 0.3);
}

[data-theme="professional"] table tbody tr:hover {
    background: rgba(58, 56, 96, 0.5);
}

/* 按钮样式调整 */
[data-theme="professional"] .btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #7c3aed 100%);
    border: none;
}

[data-theme="professional"] .btn-primary:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 输入框样式调整 */
[data-theme="professional"] input,
[data-theme="professional"] textarea,
[data-theme="professional"] select {
    background: rgba(26, 24, 64, 0.5);
    border: 1px solid rgba(58, 56, 96, 0.5);
}

[data-theme="professional"] input:focus,
[data-theme="professional"] textarea:focus,
[data-theme="professional"] select:focus {
    background: rgba(26, 24, 64, 0.7);
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 菜单项悬停效果 */
[data-theme="professional"] .menu-item:hover {
    background: rgba(58, 56, 96, 0.3);
}

[data-theme="professional"] .menu-item.active {
    background: rgba(99, 102, 241, 0.2);
}

/* 徽章和标签调整 */
[data-theme="professional"] .badge,
[data-theme="professional"] .tag {
    background: rgba(99, 102, 241, 0.2);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

/* 成功状态颜色调整 */
[data-theme="professional"] .badge-success,
[data-theme="professional"] .tag-success {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.3);
}

/* 警告状态颜色调整 */
[data-theme="professional"] .badge-warning,
[data-theme="professional"] .tag-warning {
    background: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.3);
}

/* 错误状态颜色调整 */
[data-theme="professional"] .badge-error,
[data-theme="professional"] .tag-error {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
}

/* 渐变动画效果 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 添加微妙的发光效果 */
[data-theme="professional"] .btn-primary,
[data-theme="professional"] .menu-item.active,
[data-theme="professional"] input:focus,
[data-theme="professional"] textarea:focus,
[data-theme="professional"] select:focus {
    position: relative;
}

[data-theme="professional"] .btn-primary::after,
[data-theme="professional"] .menu-item.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    opacity: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.3) 0%, rgba(139, 92, 246, 0.3) 100%);
    filter: blur(8px);
    transition: opacity 0.3s ease;
    z-index: -1;
}

[data-theme="professional"] .btn-primary:hover::after,
[data-theme="professional"] .menu-item.active::after {
    opacity: 1;
}

/* 滚动条样式 */
[data-theme="professional"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="professional"] ::-webkit-scrollbar-track {
    background: rgba(26, 24, 64, 0.3);
}

[data-theme="professional"] ::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.3);
    border-radius: 4px;
}

[data-theme="professional"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(99, 102, 241, 0.5);
}

/* 统计卡片特殊效果 */
[data-theme="professional"] .stat-card {
    background: rgba(42, 40, 80, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(58, 56, 96, 0.3);
    transition: all 0.3s ease;
}

[data-theme="professional"] .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.3);
}

/* 图表容器背景 */
[data-theme="professional"] .chart-container {
    background: rgba(26, 24, 64, 0.3);
    border: 1px solid rgba(58, 56, 96, 0.3);
    border-radius: 8px;
    padding: 16px;
}

/* 模态框背景 */
[data-theme="professional"] .modal-backdrop {
    background: rgba(10, 10, 20, 0.8);
    backdrop-filter: blur(5px);
}

/* 选项卡样式 */
[data-theme="professional"] .tab-item {
    background: transparent;
    color: #94a3b8;
}

[data-theme="professional"] .tab-item:hover {
    background: rgba(58, 56, 96, 0.2);
    color: #cbd5e1;
}

[data-theme="professional"] .tab-item.active {
    background: rgba(99, 102, 241, 0.2);
    color: #f8fafc;
    border-bottom: 2px solid #6366f1;
}

/* 侧边栏头部标题样式 */
[data-theme="professional"] .sidebar-logo h1 {
    color: #f8fafc;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 菜单部分标题 */
[data-theme="professional"] .menu-section h2 {
    color: #94a3b8;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    font-weight: 600;
}

/* 菜单项图标颜色 */
[data-theme="professional"] .menu-item i {
    color: #94a3b8;
    transition: color 0.2s ease;
}

[data-theme="professional"] .menu-item:hover i,
[data-theme="professional"] .menu-item.active i {
    color: #a5b4fc;
}

/* 徽章样式重新定义 */
[data-theme="professional"] .counter {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: #ffffff;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 头部区域样式 */
[data-theme="professional"] .header {
    background: rgba(42, 40, 80, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(58, 56, 96, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索框样式 */
[data-theme="professional"] .search-input {
    background: rgba(26, 24, 64, 0.6);
    border: 1px solid rgba(58, 56, 96, 0.3);
    color: #f8fafc;
    placeholder-color: #94a3b8;
}

[data-theme="professional"] .search-input:focus {
    background: rgba(26, 24, 64, 0.8);
    border-color: #6366f1;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 用户头像和下拉菜单 */
[data-theme="professional"] .user-avatar {
    border: 2px solid rgba(99, 102, 241, 0.3);
}

[data-theme="professional"] .user-dropdown {
    background: rgba(42, 40, 80, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(58, 56, 96, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 页面标题样式 */
[data-theme="professional"] .page-title {
    color: #f8fafc;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 内容面板样式 */
[data-theme="professional"] .content-panel {
    background: rgba(42, 40, 80, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(58, 56, 96, 0.2);
    border-radius: 12px;
}

/* 列表项样式 */
[data-theme="professional"] .list-item {
    background: rgba(26, 24, 64, 0.3);
    border: 1px solid rgba(58, 56, 96, 0.2);
    transition: all 0.2s ease;
}

[data-theme="professional"] .list-item:hover {
    background: rgba(58, 56, 96, 0.4);
    border-color: rgba(99, 102, 241, 0.3);
    transform: translateY(-1px);
}

/* 操作按钮组 */
[data-theme="professional"] .btn-group .btn {
    background: rgba(42, 40, 80, 0.6);
    border: 1px solid rgba(58, 56, 96, 0.3);
    color: #cbd5e1;
}

[data-theme="professional"] .btn-group .btn:hover {
    background: rgba(58, 56, 96, 0.7);
    border-color: rgba(99, 102, 241, 0.4);
    color: #f8fafc;
}

/* 分页器样式 */
[data-theme="professional"] .pagination .page-link {
    background: rgba(42, 40, 80, 0.6);
    border: 1px solid rgba(58, 56, 96, 0.3);
    color: #cbd5e1;
}

[data-theme="professional"] .pagination .page-link:hover {
    background: rgba(58, 56, 96, 0.7);
    color: #f8fafc;
}

[data-theme="professional"] .pagination .page-link.active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-color: #6366f1;
    color: #ffffff;
}

/* 状态指示器 */
[data-theme="professional"] .status-indicator {
    position: relative;
}

[data-theme="professional"] .status-indicator::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    top: -2px;
    right: -2px;
    box-shadow: 0 0 4px rgba(16, 185, 129, 0.5);
}